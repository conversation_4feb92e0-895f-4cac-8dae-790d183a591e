import idc
import idaapi
import ida_ida
import idautils
import traceback
import threading
import json
import base64
import hashlib
import platform
import asyncio
import aiohttp
import ida_kernwin
from queue import Queue
from typing import List, Dict, Union
from PyQt5 import QtCore, QtWidgets, QtGui
#import time

class COMModule:
    def __init__(self, 地址, 状态, 旧名称):
        self.地址 = 地址
        self.状态 = 状态
        self.旧名称 = 旧名称
        
class ComResultsModel(QtCore.QAbstractTableModel):
    # 列索引常量
    COL_名称 = 0x00
    COL_地址 = 0x01
    COL_状态 = 0x02
    COL_旧名称 = 0x03

    def __init__(self, com_module, parent=None):
        super().__init__(parent)
        # 列标题字典
        self._column_headers = {
            ComResultsModel.COL_名称: '名称',
            ComResultsModel.COL_地址: '地址',
            ComResultsModel.COL_状态: '状态',
            ComResultsModel.COL_旧名称: '旧名称'
        }
        # 存储COM模块数据
        self._results = list(com_module)
        # 行数
        self._row_count = len(self._results)

    def flags(self, index):
        # 设置表格项的标志，可启用和选择
        return QtCore.Qt.ItemIsEnabled | QtCore.Qt.ItemIsSelectable

    def rowCount(self, index=QtCore.QModelIndex()):
        # 返回表格的行数
        return self._row_count

    def columnCount(self, index=QtCore.QModelIndex()):
        # 返回表格的列数
        return len(self._column_headers)

    def headerData(self, column, orientation, role=QtCore.Qt.DisplayRole):
        """
        定义表格行和列的属性
        """
        if orientation == QtCore.Qt.Horizontal:
            # 请求列标题
            if role == QtCore.Qt.DisplayRole:
                try:
                    return self._column_headers[column]
                except KeyError as e:
                    pass
            # 请求列标题的文本对齐方式
            elif role == QtCore.Qt.TextAlignmentRole:
                # 所有列居中对齐
                return QtCore.Qt.AlignHCenter
        # 未处理的标题请求
        return None

    def data(self, index, role=QtCore.Qt.DisplayRole):
        """
        定义Qt应如何访问底层模型数据
        """
        # 请求数据显示
        if role == QtCore.Qt.DisplayRole:
            # 获取行和列索引
            row = index.row()
            column = index.column()
            if column == ComResultsModel.COL_名称:
                return str(self._results[row][0])
            if column == ComResultsModel.COL_地址:
                # return "0x%x" % self._results[row][1].地址
                return self._results[row][1].地址
            elif column == ComResultsModel.COL_状态:
                return self._results[row][1].状态
            elif column == ComResultsModel.COL_旧名称:
                return self._results[row][1].旧名称
        # 请求字体颜色
        elif role == QtCore.Qt.ForegroundRole:
            return QtGui.QColor(QtCore.Qt.black)
        # 未处理的请求，无操作
        return None

class ComResultsForm(idaapi.PluginForm):
    def __init__(self, com_modules):
        super().__init__()
        self.com_modules = com_modules

    def OnCreate(self, form):
        self._widget = self.FormToPyQtWidget(form)
        self._init_ui()

    def show(self):
        flags = idaapi.PluginForm.WOPN_TAB | idaapi.PluginForm.WOPN_PERSIST
        return idaapi.PluginForm.Show(self, "识别结果", flags)

    def _init_ui(self):
        self._model = ComResultsModel(self.com_modules, self._widget)
        self._table = QtWidgets.QTableView()
        self._table.setMinimumHeight(0)
        self._table.setSizePolicy(
            QtWidgets.QSizePolicy.Ignored,
            QtWidgets.QSizePolicy.Ignored
        )
        self._table.setModel(self._model)
        self._table.doubleClicked.connect(self._ui_entry_double_click)
        self._table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        vh = self._table.verticalHeader()
        hh = self._table.horizontalHeader()
        vh.setSectionResizeMode(QtWidgets.QHeaderView.Fixed)
        vh.hide()
        self._table.setWordWrap(True)
        self._table.setTextElideMode(QtCore.Qt.ElideMiddle)
        self._table.resizeColumnsToContents()
        self._table.resizeRowsToContents()
        layout = QtWidgets.QGridLayout()
        layout.addWidget(self._table)
        self._widget.setLayout(layout)

    def _ui_entry_double_click(self, index):
        print(self._model._results[index.row()][1].ea)
        idaapi.jumpto(self._model._results[index.row()][1].ea)
        # try:
            # row = index.row()  # 从 index 中获取行号
            # # 假设地址可以转换为整数
            # address = int(self._model._results[row][1].地址, 16) if isinstance(self._model._results[row][1].地址, str) else int(self._model._results[row][1].地址)
            # print(hex(address))
            # idaapi.jumpto(address)
        # except ValueError:
            # print("地址无法转换为整数")

class SingletonMeta(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]
        
def update_progress(current_count, total_count):
    # 通过主线程更新进度
    def update():
        progress = (current_count / total_count) * 100
        ida_kernwin.show_wait_box(f"识别进度: {progress:.2f}% ({current_count}/{total_count})")
    ida_kernwin.execute_sync(update, ida_kernwin.MFF_WRITE)

# 全局队列用于主线程任务
main_thread_queue = Queue()

main_config = {
    "url": "https://sec-lab.aliyun.com/finger/recognize/",
    "headers": {'content-type': 'application/json'},
    "timeout": 30,
    "retry_count": 3,
    "num_threads": 100,  # 并发数量
    "debug_mode": False  # False/True
}
    #"最大并发数": 20,
    #"线程池大小": 8
def main_out(*content):
    if main_config["debug_mode"]:
        print(*content)
        
# ====================== 特征提取引擎 ======================
class FunctionRecognizer:
    class FuncSigFeature(metaclass=SingletonMeta):
        def __init__(self):
            self.code_list = ["", ".text", ".plt", ".got", "extern", ".pdata", ".bss"]

            self.control_ins_list = ["call", "jc", "jnc", "jz", "jnz", "js", "jns", "jo", "jno", "jp",
                                     "jpe", "jnp", "jpo", "ja", "jnbe", "jae", "jnb", "jb", "jnae", "jbe",
                                     "jna", "je", "jne", "jg", "jnle", "jge", "jnl", "jl", "jnge", "jle", "jng"]
            self._string_list = None  # 延迟初始化
        
        @property
        def string_list(self):
            # print("开始遍历")
            # s = time.time()
            if self._string_list is None:
                self._string_list = dict()
                for seg in idautils.Strings():
                    self._string_list[str(seg)] = seg.ea
            # e = time.time()
            # print(f"已用时间：{e - s:.6f} 秒")
            return self._string_list

        def get_file_structure(self):
            try:  # Original code
                info = idaapi.get_inf_structure()
                arch = info.procName
                if info.is_be():
                    endian = "MSB"
                else:
                    endian = "LSB"
            except:  # FOR IDAPRO9.0
                arch = ida_ida.inf_get_procname()
                if ida_ida.inf_is_be():
                    endian = "MSB"
                else:
                    endian = "LSB"
            return arch, endian

        def get_file_type(self):
            file_format = ""
            file_type = ""
            try:  # Original code
                info = idaapi.get_inf_structure()
                if info.is_64bit():
                    file_format = "64"
                elif info.is_32bit():
                    file_format = "32"
                if info.filetype == idaapi.f_PE:
                    file_type = "PE"
                elif info.filetype == idaapi.f_ELF:
                    file_type = "ELF"
            except:  # FOR IDAPRO9.0
                if ida_ida.inf_is_64bit():
                    file_format = "64"
                elif ida_ida.inf_is_32bit_exactly() and not ida_ida.inf_is_64bit():
                    file_format = "32"
                if ida_ida.inf_get_filetype() == idaapi.f_PE:
                    file_type = "PE"
                elif ida_ida.inf_get_filetype() == idaapi.f_ELF:
                    file_type = "ELF"

            return file_format, file_type

        def get_module_info(self):
            module_info = ""
            if len(idc.ARGV) == 2:
                module_info = idc.ARGV[1]
            return module_info

        def byte2str(self, l):
            if "bytes" in str(type(l)):
                l = l.decode()
            return l

        def extract_const(self, ins_addr):
            const_str = ""
            op_str = idc.print_insn_mnem(ins_addr)
            if op_str not in self.control_ins_list:
                for i in range(2):
                    operand_type = idc.get_operand_type(ins_addr, i)
                    if operand_type == idc.o_mem:
                        const_addr = idc.get_operand_value(ins_addr, i)
                        if idc.get_segm_name(const_addr) not in self.code_list:
                            str_const = idc.get_strlit_contents(const_addr)
                            if str_const:
                                str_const = self.byte2str(str_const)
                                string_list = self.string_list
                                if (str_const in string_list) and (const_addr == string_list[str_const]):
                                    const_str += str_const
                                    break
            return const_str

        def get_ins_feature(self, start_ea):
            ins_str_list = list()
            ins_bytes_list = list()
            ins_list = list(idautils.FuncItems(start_ea))
            for ins_addr in ins_list:
                ins_bytes = idc.get_bytes(ins_addr, idc.get_item_size(ins_addr))
                ins_bytes_list.append(ins_bytes)
                ins_str = self.extract_const(ins_addr)
                ins_str_list.append(ins_str)
            return ins_bytes_list, ins_str_list

        def filter_segment(self, func_addr):
            ignore_list = ["extern", ".plt", ".got", ".idata"]
            if idc.get_segm_name(func_addr) in ignore_list:
                return True
            else:
                return False

    class EncodeDectory:
        def gen_msg_py2(self, content):
            content_encode = dict()
            content_encode["extmsg"] = map(base64.b64encode, content["extmsg"])
            content_encode["ins_bytes"] = map(base64.b64encode, content["ins_bytes"])
            content_encode["ins_str"] = map(base64.b64encode, content["ins_str"])
            content_encode["func_name"] = content["func_name"]
            func_id = hashlib.md5(json.dumps(content_encode).encode("utf-8")).hexdigest()
            content_encode["md5"] = func_id
            msg = json.dumps(content_encode)
            return msg, func_id

        def gen_msg_py3(self, content):
            content_encode = dict()
            content_encode["extmsg"] = [base64.b64encode(item.encode()).decode() for item in content["extmsg"]]
            content_encode["ins_bytes"] = [base64.b64encode(item).decode() for item in content["ins_bytes"]]
            content_encode["ins_str"] = [base64.b64encode(item.encode()).decode() for item in content["ins_str"]]
            content_encode["func_name"] = content["func_name"]
            func_id = hashlib.md5(json.dumps(content_encode).encode("utf-8")).hexdigest()
            content_encode["md5"] = func_id
            msg = json.dumps(content_encode)
            return msg, func_id

    def get_func_feature(self, ea):
        content = dict()
        pfn = idaapi.get_func(ea)
        if pfn:
            func_addr = pfn.start_ea
            Func = self.FuncSigFeature()
            if Func.filter_segment(func_addr):
                return None
            arch, endian = Func.get_file_structure()
            file_format, file_type = Func.get_file_type()
            module_info = Func.get_module_info()
            ins_bytes_list, ins_str_list = Func.get_ins_feature(func_addr)
            content["extmsg"] = [arch, endian, file_format, file_type, module_info]
            content["ins_bytes"] = ins_bytes_list
            content["ins_str"] = ins_str_list
            content["func_name"] = idaapi.get_func_name(func_addr)
            return content
        else:
            return None

    def get_encode_Dectory(self, ea):
        version = platform.python_version()
        func_id = ""
        symbol_dict = dict()
        Encode = self.EncodeDectory()
        if version.startswith('3'):
            msg, func_id = Encode.gen_msg_py3(ea)
        else:
            msg, func_id = Encode.gen_msg_py2(ea)
        return msg, func_id

    def get(self, address):
        func_msg = ""
        func_id = ""
        func_feat = self.get_func_feature(address)
        if func_feat:
            func_msg, func_id = self.get_encode_Dectory(func_feat)
            return func_msg, func_id
        else:
            return None

    def get2(self, md5, res):
        func_symbol = ""
        if len(res) <= 4:
            return None
        try:
            msg_dict = json.loads(res)
            func_symbol = msg_dict["func_symbol"]
            if func_symbol:
                filter_list = ["unknow", "nullsub"]
                for item in filter_list:
                    if item in func_symbol:
                        return None
                return {md5: func_symbol}
        except Exception as e:
            raise RuntimeError("get function symbol failed")
        return None

# ====================== 任务调度引擎 ======================
class FingerManager:
    def __init__(self):
        main_out("[INFO] 初始化 FingerManager")
        self.fa = FunctionRecognizer()
        self.success_results = []
        self.SModified_results = []
        self.failure_results = 0

    async def recognize_function_async(self, session, start_ea):
        main_out(f"[INFO] 开始识别函数，起始地址: 0x{start_ea:x}")
        max_retries = main_config["retry_count"]
        get_msg, get_id = self.fa.get(start_ea)
        if get_msg and get_id:
            main_out(f"[INFO] 成功提取函数特征，起始地址: 0x{start_ea:x}，MD5: {get_id}")
            main_out(f"[INFO] 请求 URL: {main_config['url']}")
            main_out(f"[INFO] 请求参数: {get_msg}")
            main_out(f"[INFO] 请求 headers: {main_config['headers']}")
            main_out(f"[INFO] 请求 timeout: {main_config['timeout']}")
            main_out(f"[INFO] 请求 retry_count: {max_retries}")
            for attempt in range(1, max_retries + 1):
                main_out(f"[INFO] 第 {attempt} 次尝试识别函数，起始地址: 0x{start_ea:x}")
                try:
                    async with session.post(main_config['url'], data=get_msg, headers=main_config['headers'], timeout=main_config['timeout']) as response:
                        if response.status == 200:
                            res = self.fa.get2(get_id, await response.text())
                            if res and res[get_id]:
                                func_symbol = res[get_id]
                                if func_symbol:
                                    return str(func_symbol)
                                else:
                                    break
                            else:
                                break
                except asyncio.TimeoutError:
                    print(f"[ERROR] 请求超时，起始地址: 0x{start_ea:x}，第 {attempt} 次尝试")
                except asyncio.exceptions.CancelledError:
                    print(f"[ERROR] 任务被取消，起始地址: 0x{start_ea:x}，第 {attempt} 次尝试")
                except (aiohttp.ClientError, json.JSONDecodeError) as e:
                    print(f"[ERROR] 请求失败，原因: {type(e).__name__}，详情: {str(e)}")
                if attempt < max_retries:
                    await asyncio.sleep(1)  # 重试前休眠
                else:
                    print(f"[INFO] 已达到最大重试次数。请求失败: 0x{start_ea:x}")
        else:
            print(f"[INFO] 函数特征提取失败，起始地址: 0x{start_ea:x}")
        return None

    def _convert_bytes_to_str(self, obj):
        if isinstance(obj, bytes):
            return obj.decode('utf-8', errors='ignore')
        elif isinstance(obj, dict):
            return {k: self._convert_bytes_to_str(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_bytes_to_str(item) for item in obj]
        return obj

    def process_function(self, pfn, current_count, total_count):
        # 将任务放入主线程队列
        main_thread_queue.put((pfn, current_count, total_count))
        
    # async def _process_function_async(self, pfn, current_count, total_count):
        # main_out(f"[INFO] 开始处理函数，起始地址: 0x{pfn.start_ea:x}")
        # update_progress(pfn, current_count, total_count)
        # async with aiohttp.ClientSession() as session:
            # func_symbol = await self.recognize_function_async(session, pfn.start_ea)
        # if func_symbol:
            # if idc.get_func_name(pfn.start_ea) != func_symbol:
                # idc.set_color(pfn.start_ea, idc.CIC_FUNC, 0x98FF98)
                # idaapi.set_name(pfn.start_ea, func_symbol, idaapi.SN_FORCE)
                # idaapi.update_func(pfn)
            # print(f"[+] 识别成功: {idc.get_func_name(pfn.start_ea)} 符号名: {func_symbol}")
        # else:
            # print(f"[-] {idc.get_func_name(pfn.start_ea)} 识别失败")
        # ida_kernwin.hide_wait_box()

    # def recognize_selected_function(self, funcs):
        # main_out("[INFO] 开始识别所选函数")
        # total_count = len(funcs)
        # semaphore = asyncio.Semaphore(100)  # 控制线程
        # async def process_function(pfn, current_count, total_count):
            # async with semaphore:
                # await self._process_function_async(pfn, current_count, total_count)

        # async def run_all_recognition():
            # async with aiohttp.ClientSession() as session:
                # tasks = []
                # for i, pfn in enumerate(funcs, start=1):
                    # task = asyncio.create_task(process_function(pfn, i, total_count))
                    # tasks.append(task)
                # await asyncio.gather(*tasks)

        # asyncio.run(run_all_recognition())
        # print("[INFO] 所选函数识别完成")
        
    async def _process_function_async(self, session, pfn, current_count, total_count):
        main_out(f"[INFO] 开始处理函数，起始地址: 0x{pfn.start_ea:x}")
        update_progress(current_count, total_count)
        func_name = idc.get_func_name(pfn.start_ea)
        func_symbol = await self.recognize_function_async(session, pfn.start_ea)
        if func_symbol:
            if func_name != func_symbol:
                idc.set_color(pfn.start_ea, idc.CIC_FUNC, 0x98FF98)
                idaapi.set_name(pfn.start_ea, func_symbol, idaapi.SN_FORCE)
                idaapi.update_func(pfn)
                self.SModified_results.append((func_symbol, "已修改", hex(pfn.start_ea), func_name))
            print(f"[+] 识别成功: {func_name} 符号名: {func_symbol}")
            self.success_results.append((func_symbol, "未修改", hex(pfn.start_ea), func_name))
        else:
            print(f"[-] {func_name} 识别失败")
            self.failure_results += 1
        ida_kernwin.hide_wait_box()

    # def recognize_selected_function(self, funcs):
        # main_out("[INFO] 开始识别所选函数")
        # total_count = len(funcs)
        # queue = Queue()
        # for i, pfn in enumerate(funcs, start=1):
            # queue.put((pfn, i, total_count))

        # def worker():
            # while not queue.empty():
                # pfn, current_count, total_count = queue.get()
                # main_out(f"[INFO] 工作线程开始处理函数，起始地址: 0x{pfn.start_ea:x}")
                # self.process_function(pfn, current_count, total_count)
                # queue.task_done()
                # main_out(f"[INFO] 工作线程完成处理函数，起始地址: 0x{pfn.start_ea:x}")

        # threads = []
        # for _ in range(main_config['num_threads']):
            # t = threading.Thread(target=worker)
            # t.start()
            # threads.append(t)

        # for t in threads:
            # t.join()

        # async def run_all_recognition():
            # async with aiohttp.ClientSession() as session:
                # tasks = []
                # while not main_thread_queue.empty():
                    # pfn, current_count, total_count = main_thread_queue.get()
                    # tasks.append(self._process_function_async(session, pfn, current_count, total_count))
                # await asyncio.gather(*tasks)
                # self.show_results_table_in_ida()

        # asyncio.run(run_all_recognition())
        # print("[INFO] 所选函数识别完成")
    def recognize_selected_function(self, funcs):
        main_out("[INFO] 开始识别所选函数")
        self.success_results = []
        self.SModified_results = []
        self.failure_results = 0
        total_count = len(funcs)
        semaphore = asyncio.Semaphore(main_config['num_threads'])  # 控制并发数量为100
        
        async def process_function(pfn, current_count, total_count):
            async with semaphore:
                async with aiohttp.ClientSession() as session:
                    await self._process_function_async(session, pfn, current_count, total_count)
        
        async def run_all_recognition():
            tasks = []
            for i, pfn in enumerate(funcs, start=1):
                task = asyncio.create_task(process_function(pfn, i, total_count))
                tasks.append(task)
            await asyncio.gather(*tasks)
            self.show_results_table_in_ida()
        asyncio.run(run_all_recognition())

    def show_results_table_in_ida(self):
        print("[INFO] 所选函数识别完成")
        print(f"[INFO] 成功识别数量: {len(self.success_results)}")
        print(f"[INFO] 成功修改数量: {len(self.SModified_results)}")
        print(f"[INFO] 失败识别数量: {self.failure_results}")
        ComResultsForm(self.success_results + self.SModified_results).show()

    def recognize_function_callback(self, menupath):
        main_out("[INFO] 触发识别单个函数回调")
        self.success_results = []
        self.SModified_results = []
        self.failure_results = 0
        ea = idaapi.get_screen_ea()
        pfn = idaapi.get_func(ea)
        if pfn:
            # 只使用异步处理，避免重复调用
            async def run_single_recognition():
                async with aiohttp.ClientSession() as session:
                    await self._process_function_async(session, pfn, 1, 1)
                # 处理完成后显示结果
                self.show_results_table_in_ida()
            asyncio.run(run_single_recognition())
        else:
            print(f"[-] 0x{ea:x} 不是一个函数")

    def recognize_functions_callback(self, menupath):
        main_out("[INFO] 触发识别所有函数回调")
        self.success_results = []
        self.SModified_results = []
        self.failure_results = 0
        funcs = []
        for ea in idautils.Functions():
            funcs.append(idaapi.get_func(ea))
        self.recognize_selected_function(funcs)


# ====================== IDA插件界面 ======================
class FingerUIManager:
    class UIHooks(idaapi.UI_Hooks):
        def finish_populating_widget_popup(self, widget, popup):
            if idaapi.get_widget_type(widget) == idaapi.BWN_FUNCS:
                main_out("[INFO] 在函数列表窗口弹出菜单中添加识别所选函数选项")
                idaapi.attach_action_to_popup(widget, popup, "Finger:RecognizeSelected", "符号识别/")
            if idaapi.get_widget_type(widget) == idaapi.BWN_DISASM:
                main_out("[INFO] 在反汇编窗口弹出菜单中添加识别单个函数选项")
                idaapi.attach_action_to_popup(widget, popup, "Finger:RecognizeFunction", "符号识别/")

    class ActionHandler(idaapi.action_handler_t):
        def __init__(self, name, label, shortcut=None, tooltip=None, icon=-1, flags=0):
            main_out(f"[INFO] 初始化动作处理程序: {name}")
            idaapi.action_handler_t.__init__(self)
            self.name = name
            self.action_desc = idaapi.action_desc_t(name, label, self, shortcut, tooltip, icon, flags)

        def register_action(self, callback, menupath=None):
            main_out(f"[INFO] 注册动作: {self.name}")
            self.callback = callback
            if not idaapi.register_action(self.action_desc):
                main_out(f"[ERROR] 动作注册失败: {self.name}")
                return False
            if menupath and not idaapi.attach_action_to_menu(menupath, self.name, idaapi.SETMENU_APP):
                main_out(f"[ERROR] 动作添加到菜单失败: {self.name}")
                return False
            main_out(f"[INFO] 动作注册成功: {self.name}")
            return True

        def activate(self, ctx):
            main_out(f"[INFO] 激活动作: {self.name}")
            self.callback(ctx)

        def update(self, ctx):
            main_out(f"[INFO] 更新动作状态: {self.name}")
            return idaapi.AST_ENABLE_ALWAYS

    def __init__(self, name):
        main_out(f"[INFO] 初始化 FingerUIManager: {name}")
        self.name = name
        self.mgr = FingerManager()
        self.hooks = FingerUIManager.UIHooks()

    def register_actions(self):
        main_out(f"[INFO] 开始注册动作: {self.name}")
        menupath = self.name
        idaapi.create_menu(menupath, self.name, "Help")

        action = FingerUIManager.ActionHandler("Finger:RecognizeFunctions", "识别所有函数", "")
        action.register_action(self.mgr.recognize_functions_callback, menupath)
        action = FingerUIManager.ActionHandler("Finger:RecognizeFunction", "识别单个函数", "")
        action.register_action(self.mgr.recognize_function_callback, menupath)
        recognize_action = FingerUIManager.ActionHandler("Finger:RecognizeSelected", "识别所选函数")
        if recognize_action.register_action(self.selected_function_callback):
            self.hooks.hook()
            main_out(f"[INFO] 动作注册完成: {self.name}")
            return True
        main_out(f"[ERROR] 动作注册失败: {self.name}")
        return False

    def selected_function_callback(self, ctx):
        main_out("[INFO] 触发识别所选函数回调")
        funcs = list(map(idaapi.getn_func, ctx.chooser_selection))
        self.mgr.recognize_selected_function(funcs)

# ====================== 插件入口 ======================
def check_ida_version():
    print("[INFO] 检查 IDA 版本")
    if idaapi.IDA_SDK_VERSION < 700:
        print("[-] 符号识别 插件仅支持 7.x 版本的 IDA，请更新您的 IDA 版本。")
        return False
    print("[INFO] IDA 版本检查通过")
    return True

class FingerPlugin(idaapi.plugin_t):
    wanted_name = "符号识别"
    comment, help, wanted_hotkey = "", "", ""
    flags = idaapi.PLUGIN_FIX | idaapi.PLUGIN_HIDE | idaapi.PLUGIN_MOD

    def init(self):
        print("[INFO] 初始化 符号识别 插件")
        try:
            if check_ida_version():
                idaapi.msg("[+] 符号识别 插件已启动\n")
                manager = FingerUIManager(FingerPlugin.wanted_name)
                if manager.register_actions():
                    print("[INFO] 符号识别 插件初始化成功")
                    return idaapi.PLUGIN_OK
        except Exception as e:
            print(f"[ERROR] 符号识别 插件初始化时出现异常: {e}")
        print("[INFO] 符号识别 插件初始化失败")
        return idaapi.PLUGIN_SKIP

    def run(self, ctx):
        print("[INFO] 运行 符号识别 插件")
        return

    def term(self):
        print("[INFO] 终止 符号识别 插件")
        return

def PLUGIN_ENTRY():
    main_out("[INFO] 进入插件入口")
    return FingerPlugin()

    