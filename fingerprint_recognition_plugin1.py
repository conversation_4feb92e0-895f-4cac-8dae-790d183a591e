import idc
import idaapi
import ida_ida
import idautils
import traceback
import threading
import json
import base64
import hashlib
import platform
import asyncio
import aiohttp
from queue import Queue
from typing import List, Dict, Union
import ida_kernwin


def update_progress(current_count, total_count):
    """通过主线程更新进度"""
    progress = (current_count / total_count) * 100
    ida_kernwin.msg(f"Progress: {progress:.2f}%\n")


# 全局队列用于主线程任务
main_thread_queue = Queue()

main_config = {
    "url": "https://sec-lab.aliyun.com/finger/recognize/",
    "headers": {'content-type': 'application/json'},
    "timeout": 30,
    "retry_count": 3,
    "num_threads": 4,  # 线程数量
    "debug_mode": False  # False/True
}


def main_out(*content):
    """输出信息"""
    message = " ".join(str(c) for c in content)
    ida_kernwin.msg(message + "\n")


# ====================== 特征提取引擎 ======================
class FunctionRecognizer:
    class FuncSigFeature:
        def get_file_structure(self):
            """获取文件结构信息，这里简单返回None，实际中可实现具体逻辑"""
            return None

        def get_file_type(self):
            """获取文件类型信息，这里简单返回None，实际中可实现具体逻辑"""
            return None

        def get_module_info(self):
            """获取模块信息，这里简单返回None，实际中可实现具体逻辑"""
            return None

        def byte2str(self, l):
            """字节转字符串"""
            return str(l)

        def extract_const(self, ins_addr):
            """提取常量，这里简单返回None，实际中可实现具体逻辑"""
            return None

        def get_ins_feature(self, start_ea):
            """获取指令特征，这里简单返回空字符串，实际中可实现具体逻辑"""
            ins_bytes = []
            for ins in idautils.Heads(start_ea, idaapi.get_func_attr(start_ea, idaapi.FUNCATTR_END)):
                ins_bytes.extend(idc.get_bytes(ins, idc.get_item_size(ins)))
            return bytes(ins_bytes).hex()

        def filter_segment(self, func_addr):
            """过滤段，这里简单返回True，实际中可实现具体逻辑"""
            return True

    class EncodeDectory:
        def gen_msg_py2(self, content):
            """生成Python2消息"""
            return json.dumps(content).encode('utf-8')

        def gen_msg_py3(self, content):
            """生成Python3消息"""
            return json.dumps(content).encode('utf-8')

    def __init__(self):
        self.fa = self.FuncSigFeature()
        self.ed = self.EncodeDectory()

    def get_func_feature(self, ea):
        """获取函数特征"""
        return self.fa.get_ins_feature(ea)

    def get_encode_Dectory(self, ea):
        """获取编码目录"""
        feature = self.get_func_feature(ea)
        return self.ed.gen_msg_py3({"feature": feature})

    def get(self, address):
        """获取消息和ID"""
        get_msg = self.get_encode_Dectory(address)
        get_id = hashlib.md5(get_msg).hexdigest()
        return get_msg, get_id

    def get2(self, md5, res):
        """处理响应结果"""
        try:
            result = json.loads(res)
            return {md5: result.get(md5)}
        except json.JSONDecodeError:
            return None


# ====================== 任务调度引擎 ======================
class FingerManager:
    def __init__(self):
        self.success_count = 0
        self.failure_count = 0
        self.success_functions = []
        self.failure_functions = []

    async def recognize_function_async(self, session, start_ea):
        try:
            fa = FunctionRecognizer()
            get_msg, get_id = fa.get(start_ea)
            for _ in range(main_config["retry_count"]):
                try:
                    async with session.post(
                            main_config["url"], data=get_msg, headers=main_config["headers"],
                            timeout=main_config["timeout"]
                    ) as response:
                        if response.status == 200:
                            res = fa.get2(get_id, await response.text())
                            if res and res[get_id]:
                                self.success_count += 1
                                self.success_functions.append(start_ea)
                                return str(res[get_id])
                except asyncio.TimeoutError:
                    continue
            self.failure_count += 1
            self.failure_functions.append(start_ea)
        except Exception as e:
            self.failure_count += 1
            self.failure_functions.append(start_ea)
        return None

    def _convert_bytes_to_str(self, obj):
        """字节转字符串"""
        if isinstance(obj, bytes):
            return obj.decode('utf-8')
        return obj

    def process_function(self, pfn, current_count, total_count):
        """将任务放入主线程队列"""
        def task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                session = aiohttp.ClientSession()
                loop.run_until_complete(self.recognize_function_async(session, pfn))
                loop.run_until_complete(session.close())
            finally:
                loop.close()
            update_progress(current_count, total_count)

        threading.Thread(target=task).start()

    async def _process_function_async(self, session, pfn, current_count, total_count):
        await self.recognize_function_async(session, pfn)
        update_progress(current_count, total_count)

    def recognize_selected_function(self, funcs):
        async def run():
            async with aiohttp.ClientSession() as session:
                tasks = [
                    self._process_function_async(session, func, i + 1, len(funcs))
                    for i, func in enumerate(funcs)
                ]
                await asyncio.gather(*tasks)
            self.show_statistics()

        asyncio.run(run())

    def recognize_function_callback(self, menupath):
        """单个函数识别回调"""
        func_ea = idaapi.get_screen_ea()
        self.recognize_selected_function([func_ea])

    def recognize_functions_callback(self, menupath):
        """多个函数识别回调"""
        funcs = []
        for func_ea in idautils.Functions():
            funcs.append(func_ea)
        self.recognize_selected_function(funcs)

    def show_statistics(self):
        """显示统计信息"""
        # 显示成功表格
        ida_kernwin.msg("Success Table:\n")
        ida_kernwin.msg("Function Address\n")
        ida_kernwin.msg("-" * 20 + "\n")
        for func in self.success_functions:
            ida_kernwin.msg(f"{hex(func)}\n")

        # 显示失败表格
        ida_kernwin.msg("\nFailure Table:\n")
        ida_kernwin.msg("Function Address\n")
        ida_kernwin.msg("-" * 20 + "\n")
        for func in self.failure_functions:
            ida_kernwin.msg(f"{hex(func)}\n")


# ====================== IDA插件界面 ======================
class FingerUIManager:
    class UIHooks(idaapi.UI_Hooks):
        def finish_populating_widget_popup(self, widget, popup):
            """完成填充小部件弹出菜单"""
            idaapi.attach_action_to_popup(widget, popup, "FingerPlugin:RecognizeFunction", None)
            idaapi.attach_action_to_popup(widget, popup, "FingerPlugin:RecognizeFunctions", None)

    class ActionHandler(idaapi.action_handler_t):
        def __init__(self, name, label, shortcut=None, tooltip=None, icon=-1, flags=0):
            super().__init__()
            self.name = name
            self.label = label
            self.shortcut = shortcut
            self.tooltip = tooltip
            self.icon = icon
            self.flags = flags
            self.callback = None

        def register_action(self, callback, menupath=None):
            """注册动作"""
            self.callback = callback
            action_desc = idaapi.action_desc_t(
                self.name, self.label, self, self.shortcut, self.tooltip, self.icon
            )
            idaapi.register_action(action_desc)
            if menupath:
                idaapi.attach_action_to_menu(menupath, self.name, idaapi.SETMENU_APP)

        def activate(self, ctx):
            """激活动作"""
            if self.callback:
                self.callback(ctx)
            return 1

        def update(self, ctx):
            """更新动作"""
            return idaapi.AST_ENABLE_ALWAYS

    def __init__(self, name):
        self.name = name
        self.ui_hooks = self.UIHooks()
        self.ui_hooks.hook()
        self.recognize_function_action = self.ActionHandler(
            f"{name}:RecognizeFunction", "Recognize Function", None, "Recognize the current function"
        )
        self.recognize_functions_action = self.ActionHandler(
            f"{name}:RecognizeFunctions", "Recognize All Functions", None, "Recognize all functions in the binary"
        )

    def register_actions(self):
        """注册动作"""
        manager = FingerManager()
        self.recognize_function_action.register_action(manager.recognize_function_callback, "Edit/FingerPlugin/")
        self.recognize_functions_action.register_action(manager.recognize_functions_callback, "Edit/FingerPlugin/")


# ====================== 插件入口 ======================
def check_ida_version():
    """检查IDA版本"""
    major, minor = map(int, ida_ida.inf_get_minver().split('.'))
    if major < 7:
        ida_kernwin.warning("This plugin requires IDA Pro 7.0 or higher.")
        return False
    return True


class FingerPlugin(idaapi.plugin_t):
    flags = idaapi.PLUGIN_UNL
    comment = "Fingerprint recognition plugin"
    help = ""
    wanted_name = "FingerPlugin"
    wanted_hotkey = ""

    def init(self):
        """插件初始化"""
        if not check_ida_version():
            return idaapi.PLUGIN_SKIP
        ui_manager = FingerUIManager(self.wanted_name)
        ui_manager.register_actions()
        return idaapi.PLUGIN_KEEP

    def run(self, ctx):
        """插件运行"""
        manager = FingerManager()
        # 模拟选择的函数列表
        selected_functions = [func_ea for func_ea in idautils.Functions()]
        manager.recognize_selected_function(selected_functions)

    def term(self):
        """插件终止"""
        pass


def PLUGIN_ENTRY():
    return FingerPlugin()
    