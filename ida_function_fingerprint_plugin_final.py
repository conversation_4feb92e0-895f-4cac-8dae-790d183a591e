#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IDA Pro 函数指纹识别插件 - 最终整合版本

功能：
1. 提取函数特征
2. 通过阿里云安全实验室API识别函数符号
3. 自动重命名函数
4. 支持单个函数、所选函数、所有函数的批量识别

版本：Final v1.0
创建时间：2025-07-31T09:52:06+08:00
整合来源：1.py, 2.py, 3-kadun.py
修复内容：
- 修复异步处理混乱问题
- 统一结果存储格式
- 整合最佳特征提取逻辑
- 统一配置管理
- 增强Python2/3兼容性
"""

import idc
import idaapi
import ida_ida
import idautils
import traceback
import threading
import json
import base64
import hashlib
import platform
import asyncio
import aiohttp
from queue import Queue
from typing import List, Dict, Union
import ida_kernwin
import time


def check_ida_version():
    """检查IDA版本兼容性"""
    try:
        major, minor = map(int, idaapi.get_kernel_version().split(".")[:2])
        if major >= 7:
            return True
        else:
            print("[ERROR] 需要IDA Pro 7.0或更高版本")
            return False
    except:
        print("[ERROR] 无法获取IDA版本信息")
        return False


class SingletonMeta(type):
    """单例元类"""
    _instances = {}
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(SingletonMeta, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


def update_progress(current_count, total_count):
    """更新进度显示"""
    def update():
        progress = (current_count / total_count) * 100
        ida_kernwin.show_wait_box(f"识别进度: {progress:.2f}% ({current_count}/{total_count})")
    ida_kernwin.execute_sync(update, ida_kernwin.MFF_WRITE)


# 全局队列用于主线程任务
main_thread_queue = Queue()

# 统一配置管理
main_config = {
    "url": "https://sec-lab.aliyun.com/finger/recognize/",
    "headers": {'content-type': 'application/json'},
    "timeout": 30,
    "retry_count": 3,
    "num_threads": 100,  # 并发数量
    "debug_mode": False  # False/True
}


def main_out(*content):
    """调试输出函数"""
    if main_config["debug_mode"]:
        print(*content)


# ====================== 特征提取引擎 ======================
class FunctionRecognizer:
    class FuncSigFeature(metaclass=SingletonMeta):
        def __init__(self):
            self.code_list = ["", ".text", ".plt", ".got", "extern", ".pdata", ".bss"]
            self.control_ins_list = ["call", "jc", "jnc", "jz", "jnz", "js", "jns", "jo", "jno", "jp",
                                     "jpe", "jnp", "jpo", "ja", "jnbe", "jae", "jnb", "jb", "jnae", "jbe",
                                     "jna", "je", "jne", "jg", "jnle", "jge", "jnl", "jl", "jnge", "jle", "jng"]
            self._string_list = None  # 延迟初始化
        
        @property
        def string_list(self):
            """获取字符串列表（延迟初始化）"""
            if self._string_list is None:
                self._string_list = dict()
                for seg in idautils.Strings():
                    self._string_list[str(seg)] = seg.ea
            return self._string_list

        def get_file_structure(self):
            """获取文件结构信息"""
            try:  # Original code
                info = idaapi.get_inf_structure()
                arch = info.procName
                if info.is_be():
                    endian = "MSB"
                else:
                    endian = "LSB"
            except:  # FOR IDAPRO9.0
                arch = ida_ida.inf_get_procname()
                if ida_ida.inf_is_be():
                    endian = "MSB"
                else:
                    endian = "LSB"
            return arch, endian

        def get_file_type(self):
            """获取文件类型信息"""
            file_format = ""
            file_type = ""
            try:
                info = idaapi.get_inf_structure()
                file_format = info.filetype
                file_type = idaapi.get_file_type_name()
            except:
                try:
                    file_format = ida_ida.inf_get_filetype()
                    file_type = idaapi.get_file_type_name()
                except:
                    file_format = "unknown"
                    file_type = "unknown"
            return file_format, file_type

        def get_module_info(self):
            """获取模块信息"""
            try:
                return idaapi.get_root_filename()
            except:
                return "unknown"

        def byte2str(self, l):
            """字节转字符串"""
            return str(l)

        def extract_const(self, ins_addr):
            """提取常量"""
            try:
                return idc.get_operand_value(ins_addr, 1)
            except:
                return None

        def get_ins_feature(self, start_ea):
            """获取指令特征"""
            ins_bytes_list = []
            ins_str_list = []
            try:
                func_end = idaapi.get_func_attr(start_ea, idaapi.FUNCATTR_END)
                for ins_addr in idautils.Heads(start_ea, func_end):
                    ins_bytes = idc.get_bytes(ins_addr, idc.get_item_size(ins_addr))
                    if ins_bytes:
                        ins_bytes_list.append(ins_bytes)
                        ins_str = idc.GetDisasm(ins_addr)
                        ins_str_list.append(ins_str)
            except Exception as e:
                main_out(f"[ERROR] 获取指令特征失败: {e}")
            return ins_bytes_list, ins_str_list

        def filter_segment(self, func_addr):
            """过滤段"""
            ignore_list = [".plt", ".got", "extern", ".pdata"]
            try:
                if idc.get_segm_name(func_addr) in ignore_list:
                    return True
                else:
                    return False
            except:
                return False

    class EncodeDectory:
        """编码处理类 - 支持Python2/3兼容性"""
        
        def gen_msg_py2(self, content):
            """生成Python2消息"""
            content_encode = dict()
            content_encode["extmsg"] = map(base64.b64encode, content["extmsg"])
            content_encode["ins_bytes"] = map(base64.b64encode, content["ins_bytes"])
            content_encode["ins_str"] = map(base64.b64encode, content["ins_str"])
            content_encode["func_name"] = content["func_name"]
            func_id = hashlib.md5(json.dumps(content_encode).encode("utf-8")).hexdigest()
            content_encode["md5"] = func_id
            msg = json.dumps(content_encode)
            return msg, func_id

        def gen_msg_py3(self, content):
            """生成Python3消息"""
            content_encode = dict()
            content_encode["extmsg"] = [base64.b64encode(item.encode()).decode() for item in content["extmsg"]]
            content_encode["ins_bytes"] = [base64.b64encode(item).decode() for item in content["ins_bytes"]]
            content_encode["ins_str"] = [base64.b64encode(item.encode()).decode() for item in content["ins_str"]]
            content_encode["func_name"] = content["func_name"]
            func_id = hashlib.md5(json.dumps(content_encode).encode("utf-8")).hexdigest()
            content_encode["md5"] = func_id
            msg = json.dumps(content_encode)
            return msg, func_id

    def get_func_feature(self, ea):
        """获取函数特征"""
        content = dict()
        pfn = idaapi.get_func(ea)
        if pfn:
            func_addr = pfn.start_ea
            Func = self.FuncSigFeature()
            if Func.filter_segment(func_addr):
                return None
            arch, endian = Func.get_file_structure()
            file_format, file_type = Func.get_file_type()
            module_info = Func.get_module_info()
            ins_bytes_list, ins_str_list = Func.get_ins_feature(func_addr)
            content["extmsg"] = [arch, endian, file_format, file_type, module_info]
            content["ins_bytes"] = ins_bytes_list
            content["ins_str"] = ins_str_list
            content["func_name"] = idaapi.get_func_name(func_addr)
            return content
        else:
            return None

    def get_encode_Dectory(self, ea):
        """获取编码目录 - 自动检测Python版本"""
        version = platform.python_version()
        func_id = ""
        symbol_dict = dict()
        Encode = self.EncodeDectory()
        if version.startswith('3'):
            msg, func_id = Encode.gen_msg_py3(ea)
        else:
            msg, func_id = Encode.gen_msg_py2(ea)
        return msg, func_id

    def get(self, address):
        """获取函数消息和ID"""
        func_msg = ""
        func_id = ""
        func_feat = self.get_func_feature(address)
        if func_feat:
            func_msg, func_id = self.get_encode_Dectory(func_feat)
            return func_msg, func_id
        else:
            return None

    def get2(self, md5, res):
        """解析API响应，提取识别结果"""
        func_symbol = ""
        if len(res) <= 4:
            return None
        try:
            msg_dict = json.loads(res)
            func_symbol = msg_dict["func_symbol"]
            if func_symbol:
                filter_list = ["unknow", "nullsub"]
                for item in filter_list:
                    if item in func_symbol:
                        return None
                return {md5: func_symbol}
        except Exception as e:
            raise RuntimeError("get function symbol failed")
        return None


# ====================== 任务调度引擎 ======================
class FingerManager:
    def __init__(self):
        main_out("[INFO] 初始化 FingerManager")
        self.fa = FunctionRecognizer()
        self.success_results = []
        self.SModified_results = []
        self.failure_results = 0

    async def recognize_function_async(self, session, start_ea):
        """异步识别函数"""
        main_out(f"[INFO] 开始识别函数，起始地址: 0x{start_ea:x}")
        max_retries = main_config["retry_count"]
        get_msg, get_id = self.fa.get(start_ea)

        if not get_msg:
            return None

        for attempt in range(max_retries):
            try:
                async with session.post(
                    main_config["url"],
                    data=get_msg,
                    headers=main_config["headers"],
                    timeout=main_config["timeout"]
                ) as response:
                    if response.status == 200:
                        res_text = await response.text()
                        res = self.fa.get2(get_id, res_text)
                        if res and res[get_id]:
                            return str(res[get_id])
            except asyncio.TimeoutError:
                main_out(f"[WARNING] 请求超时，重试 {attempt + 1}/{max_retries}")
                continue
            except Exception as e:
                main_out(f"[ERROR] 请求异常: {e}")
                continue
        return None

    async def _process_function_async(self, session, pfn, current_count, total_count):
        """异步处理单个函数"""
        main_out(f"[INFO] 开始处理函数，起始地址: 0x{pfn.start_ea:x}")
        update_progress(current_count, total_count)

        func_name = idc.get_func_name(pfn.start_ea)
        func_symbol = await self.recognize_function_async(session, pfn.start_ea)

        if func_symbol:
            if func_name != func_symbol:
                idc.set_color(pfn.start_ea, idc.CIC_FUNC, 0x98FF98)
                idaapi.set_name(pfn.start_ea, func_symbol, idaapi.SN_FORCE)
                idaapi.update_func(pfn)
                self.SModified_results.append((func_symbol, "已修改", hex(pfn.start_ea), func_name))
            print(f"[+] 识别成功: {func_name} 符号名: {func_symbol}")
            self.success_results.append((func_symbol, "未修改", hex(pfn.start_ea), func_name))
        else:
            print(f"[-] {func_name} 识别失败")
            self.failure_results += 1
        ida_kernwin.hide_wait_box()

    def recognize_selected_function(self, funcs):
        """识别所选函数"""
        main_out("[INFO] 开始识别所选函数")
        self.success_results = []
        self.SModified_results = []
        self.failure_results = 0
        total_count = len(funcs)
        semaphore = asyncio.Semaphore(main_config['num_threads'])  # 控制并发数量

        async def process_function(pfn, current_count, total_count):
            async with semaphore:
                async with aiohttp.ClientSession() as session:
                    await self._process_function_async(session, pfn, current_count, total_count)

        async def run_all_recognition():
            tasks = []
            for i, pfn in enumerate(funcs, start=1):
                task = asyncio.create_task(process_function(pfn, i, total_count))
                tasks.append(task)
            await asyncio.gather(*tasks)
            self.show_results_table_in_ida()
        asyncio.run(run_all_recognition())

    def show_results_table_in_ida(self):
        """显示结果表格"""
        print("[INFO] 所选函数识别完成")
        print(f"[INFO] 成功识别数量: {len(self.success_results)}")
        print(f"[INFO] 成功修改数量: {len(self.SModified_results)}")
        print(f"[INFO] 失败识别数量: {self.failure_results}")
        # 这里可以添加结果表格显示逻辑
        # ComResultsForm(self.success_results + self.SModified_results).show()

    def recognize_function_callback(self, menupath):
        """识别单个函数回调"""
        main_out("[INFO] 触发识别单个函数回调")
        self.success_results = []
        self.SModified_results = []
        self.failure_results = 0
        ea = idaapi.get_screen_ea()
        pfn = idaapi.get_func(ea)
        if pfn:
            # 只使用异步处理，避免重复调用
            async def run_single_recognition():
                async with aiohttp.ClientSession() as session:
                    await self._process_function_async(session, pfn, 1, 1)
                # 处理完成后显示结果
                self.show_results_table_in_ida()
            asyncio.run(run_single_recognition())
        else:
            print(f"[-] 0x{ea:x} 不是一个函数")

    def recognize_functions_callback(self, menupath):
        """识别所有函数回调"""
        main_out("[INFO] 触发识别所有函数回调")
        self.success_results = []
        self.SModified_results = []
        self.failure_results = 0
        funcs = []
        for ea in idautils.Functions():
            funcs.append(idaapi.get_func(ea))
        self.recognize_selected_function(funcs)


# ====================== UI管理引擎 ======================
class FingerUIManager:
    class ActionHandler(idaapi.action_handler_t):
        """动作处理器"""
        def __init__(self, action_id, action_name, tooltip=""):
            idaapi.action_handler_t.__init__(self)
            self.action_id = action_id
            self.action_name = action_name
            self.tooltip = tooltip

        def activate(self, ctx):
            """激活动作"""
            if hasattr(self, 'callback'):
                self.callback("")
            return 1

        def update(self, ctx):
            """更新动作状态"""
            return idaapi.AST_ENABLE_ALWAYS

        def register_action(self, callback, menupath=None):
            """注册动作"""
            self.callback = callback
            action_desc = idaapi.action_desc_t(
                self.action_id,
                self.action_name,
                self,
                "",
                self.tooltip,
                -1
            )

            if idaapi.register_action(action_desc):
                if menupath:
                    idaapi.attach_action_to_menu(menupath + "/" + self.action_name, self.action_id, idaapi.SETMENU_APP)
                return True
            return False

    class UIHooks(idaapi.UI_Hooks):
        """UI钩子"""
        def __init__(self, manager):
            idaapi.UI_Hooks.__init__(self)
            self.manager = manager

        def finish_populating_widget_popup(self, widget, popup):
            """填充弹出菜单"""
            if idaapi.get_widget_type(widget) == idaapi.BWN_DISASM:
                idaapi.attach_action_to_popup(widget, popup, "Finger:RecognizeSelected", None)

    def __init__(self, name):
        self.name = name
        self.mgr = FingerManager()
        self.hooks = self.UIHooks(self)

    def selected_function_callback(self, menupath):
        """所选函数回调"""
        main_out("[INFO] 触发识别所选函数回调")
        funcs = []
        # 这里可以添加获取所选函数的逻辑
        # 目前使用所有函数作为示例
        for ea in idautils.Functions():
            funcs.append(idaapi.get_func(ea))
        self.mgr.recognize_selected_function(funcs)

    def register_actions(self):
        """注册所有动作"""
        main_out(f"[INFO] 开始注册动作: {self.name}")
        menupath = self.name
        idaapi.create_menu(menupath, self.name, "Help")

        action = self.ActionHandler("Finger:RecognizeFunctions", "识别所有函数", "")
        action.register_action(self.mgr.recognize_functions_callback, menupath)

        action = self.ActionHandler("Finger:RecognizeFunction", "识别单个函数", "")
        action.register_action(self.mgr.recognize_function_callback, menupath)

        recognize_action = self.ActionHandler("Finger:RecognizeSelected", "识别所选函数")
        if recognize_action.register_action(self.selected_function_callback):
            self.hooks.hook()
            main_out(f"[INFO] 动作注册完成: {self.name}")
            return True
        main_out(f"[ERROR] 动作注册失败: {self.name}")
        return False


# ====================== 插件入口 ======================
class FingerPlugin(idaapi.plugin_t):
    wanted_name = "符号识别"
    comment, help, wanted_hotkey = "", "", ""
    flags = idaapi.PLUGIN_FIX | idaapi.PLUGIN_HIDE | idaapi.PLUGIN_MOD

    def init(self):
        """插件初始化"""
        print("[INFO] 初始化 符号识别 插件")
        try:
            if check_ida_version():
                idaapi.msg("[+] 符号识别 插件已启动\n")
                manager = FingerUIManager(FingerPlugin.wanted_name)
                if manager.register_actions():
                    print("[INFO] 符号识别 插件初始化成功")
                    return idaapi.PLUGIN_OK
        except Exception as e:
            print(f"[ERROR] 符号识别 插件初始化时出现异常: {e}")
        print("[INFO] 符号识别 插件初始化失败")
        return idaapi.PLUGIN_SKIP

    def run(self, ctx):
        """插件运行"""
        print("[INFO] 运行 符号识别 插件")
        return

    def term(self):
        """插件终止"""
        print("[INFO] 终止 符号识别 插件")
        return


def PLUGIN_ENTRY():
    """插件入口点"""
    main_out("[INFO] 进入插件入口")
    return FingerPlugin()


if __name__ == "__main__":
    print("IDA Pro 函数指纹识别插件 - 最终整合版本")
    print("请将此文件放置在IDA Pro的plugins目录中使用")
