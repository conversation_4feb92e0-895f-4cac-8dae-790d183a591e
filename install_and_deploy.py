#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IDA Pro 函数指纹识别插件 - 安装和部署脚本

功能：
1. 检查环境依赖
2. 安装必要的Python包
3. 部署插件到IDA Pro
4. 验证安装结果

使用方法：
python install_and_deploy.py [--ida-path /path/to/ida]
"""

import os
import sys
import shutil
import subprocess
import argparse
import platform
from pathlib import Path


class PluginInstaller:
    def __init__(self, ida_path=None):
        self.ida_path = ida_path
        self.plugin_file = "ida_function_fingerprint_plugin_final.py"
        self.required_packages = ["aiohttp"]
        self.system = platform.system().lower()
        
    def find_ida_installation(self):
        """查找IDA Pro安装路径"""
        if self.ida_path and os.path.exists(self.ida_path):
            return self.ida_path
            
        # 常见的IDA Pro安装路径
        common_paths = []
        
        if self.system == "windows":
            common_paths = [
                r"C:\Program Files\IDA Pro 7.7",
                r"C:\Program Files\IDA Pro 7.6", 
                r"C:\Program Files\IDA Pro 7.5",
                r"C:\Program Files (x86)\IDA Pro 7.7",
                r"C:\Program Files (x86)\IDA Pro 7.6",
                r"C:\Program Files (x86)\IDA Pro 7.5",
            ]
        elif self.system == "darwin":  # macOS
            common_paths = [
                "/Applications/IDA Pro 7.7/ida.app",
                "/Applications/IDA Pro 7.6/ida.app",
                "/Applications/IDA Pro 7.5/ida.app",
            ]
        else:  # Linux
            common_paths = [
                "/opt/ida-7.7",
                "/opt/ida-7.6", 
                "/opt/ida-7.5",
                "/usr/local/ida",
            ]
        
        for path in common_paths:
            if os.path.exists(path):
                print(f"✅ 找到IDA Pro安装路径: {path}")
                return path
        
        print("❌ 未找到IDA Pro安装路径")
        return None
    
    def get_plugins_directory(self, ida_path):
        """获取插件目录路径"""
        if self.system == "windows":
            plugins_dir = os.path.join(ida_path, "plugins")
        elif self.system == "darwin":
            plugins_dir = os.path.join(ida_path, "Contents", "MacOS", "plugins")
        else:
            plugins_dir = os.path.join(ida_path, "plugins")
        
        return plugins_dir
    
    def check_python_environment(self):
        """检查Python环境"""
        print("\n=== Python环境检查 ===")
        
        # 检查Python版本
        python_version = sys.version_info
        print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
            print("⚠️  建议使用Python 3.6或更高版本")
        else:
            print("✅ Python版本符合要求")
        
        # 检查pip
        try:
            import pip
            print("✅ pip可用")
            return True
        except ImportError:
            print("❌ pip不可用，请先安装pip")
            return False
    
    def install_dependencies(self):
        """安装依赖包"""
        print("\n=== 安装依赖包 ===")
        
        for package in self.required_packages:
            try:
                __import__(package)
                print(f"✅ {package} 已安装")
            except ImportError:
                print(f"📦 正在安装 {package}...")
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    print(f"✅ {package} 安装成功")
                except subprocess.CalledProcessError as e:
                    print(f"❌ {package} 安装失败: {e}")
                    return False
        
        return True
    
    def backup_existing_plugin(self, plugins_dir):
        """备份现有插件"""
        plugin_path = os.path.join(plugins_dir, self.plugin_file)
        if os.path.exists(plugin_path):
            backup_path = plugin_path + ".backup"
            shutil.copy2(plugin_path, backup_path)
            print(f"✅ 已备份现有插件到: {backup_path}")
    
    def deploy_plugin(self, ida_path):
        """部署插件"""
        print(f"\n=== 部署插件到IDA Pro ===")
        
        plugins_dir = self.get_plugins_directory(ida_path)
        
        # 检查插件目录是否存在
        if not os.path.exists(plugins_dir):
            print(f"❌ 插件目录不存在: {plugins_dir}")
            return False
        
        print(f"插件目录: {plugins_dir}")
        
        # 检查源文件是否存在
        if not os.path.exists(self.plugin_file):
            print(f"❌ 源插件文件不存在: {self.plugin_file}")
            return False
        
        # 备份现有插件
        self.backup_existing_plugin(plugins_dir)
        
        # 复制插件文件
        try:
            dest_path = os.path.join(plugins_dir, self.plugin_file)
            shutil.copy2(self.plugin_file, dest_path)
            print(f"✅ 插件部署成功: {dest_path}")
            return True
        except Exception as e:
            print(f"❌ 插件部署失败: {e}")
            return False
    
    def verify_installation(self, ida_path):
        """验证安装"""
        print(f"\n=== 验证安装 ===")
        
        plugins_dir = self.get_plugins_directory(ida_path)
        plugin_path = os.path.join(plugins_dir, self.plugin_file)
        
        if os.path.exists(plugin_path):
            file_size = os.path.getsize(plugin_path)
            print(f"✅ 插件文件存在: {plugin_path}")
            print(f"✅ 文件大小: {file_size} 字节")
            
            # 简单的语法检查
            try:
                with open(plugin_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                compile(content, plugin_path, 'exec')
                print("✅ 插件语法检查通过")
                return True
            except SyntaxError as e:
                print(f"❌ 插件语法错误: {e}")
                return False
        else:
            print(f"❌ 插件文件不存在: {plugin_path}")
            return False
    
    def create_uninstall_script(self, ida_path):
        """创建卸载脚本"""
        print(f"\n=== 创建卸载脚本 ===")
        
        plugins_dir = self.get_plugins_directory(ida_path)
        plugin_path = os.path.join(plugins_dir, self.plugin_file)
        
        uninstall_script = f"""#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
IDA Pro 函数指纹识别插件 - 卸载脚本
自动生成于安装过程
\"\"\"

import os
import sys

def uninstall():
    plugin_path = r"{plugin_path}"
    backup_path = plugin_path + ".backup"
    
    print("=== IDA Pro 函数指纹识别插件卸载 ===")
    
    # 删除插件文件
    if os.path.exists(plugin_path):
        try:
            os.remove(plugin_path)
            print(f"✅ 已删除插件: {{plugin_path}}")
        except Exception as e:
            print(f"❌ 删除插件失败: {{e}}")
            return False
    
    # 恢复备份文件
    if os.path.exists(backup_path):
        try:
            os.rename(backup_path, plugin_path)
            print(f"✅ 已恢复备份: {{backup_path}}")
        except Exception as e:
            print(f"❌ 恢复备份失败: {{e}}")
    
    print("✅ 卸载完成")
    return True

if __name__ == "__main__":
    uninstall()
"""
        
        try:
            with open("uninstall_plugin.py", 'w', encoding='utf-8') as f:
                f.write(uninstall_script)
            print("✅ 卸载脚本已创建: uninstall_plugin.py")
        except Exception as e:
            print(f"❌ 创建卸载脚本失败: {e}")
    
    def run_installation(self):
        """运行完整安装流程"""
        print("=" * 60)
        print("IDA Pro 函数指纹识别插件 - 安装程序")
        print("=" * 60)
        
        # 1. 检查Python环境
        if not self.check_python_environment():
            return False
        
        # 2. 安装依赖
        if not self.install_dependencies():
            return False
        
        # 3. 查找IDA Pro
        ida_path = self.find_ida_installation()
        if not ida_path:
            print("\n请手动指定IDA Pro安装路径:")
            print("python install_and_deploy.py --ida-path /path/to/ida")
            return False
        
        # 4. 部署插件
        if not self.deploy_plugin(ida_path):
            return False
        
        # 5. 验证安装
        if not self.verify_installation(ida_path):
            return False
        
        # 6. 创建卸载脚本
        self.create_uninstall_script(ida_path)
        
        print("\n" + "=" * 60)
        print("🎉 安装完成!")
        print("=" * 60)
        print("下一步:")
        print("1. 启动IDA Pro")
        print("2. 在菜单栏中找到'符号识别'菜单")
        print("3. 选择相应的识别功能")
        print("\n如需卸载，请运行: python uninstall_plugin.py")
        
        return True


def main():
    parser = argparse.ArgumentParser(description="IDA Pro 函数指纹识别插件安装程序")
    parser.add_argument("--ida-path", help="IDA Pro安装路径")
    args = parser.parse_args()
    
    installer = PluginInstaller(args.ida_path)
    success = installer.run_installation()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
