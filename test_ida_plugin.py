#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IDA Pro 函数指纹识别插件测试脚本

功能：
1. 语法检查
2. 导入测试
3. 基本功能测试
4. 配置验证

使用方法：
python test_ida_plugin.py

注意：此脚本在非IDA环境中运行，主要用于语法和基本逻辑检查
"""

import sys
import os
import json
import ast
import importlib.util


class MockIDAAPI:
    """模拟IDA API用于测试"""
    
    def get_kernel_version(self):
        return "7.5"
    
    def get_screen_ea(self):
        return 0x401000
    
    def get_func(self, ea):
        class MockFunc:
            def __init__(self):
                self.start_ea = ea
        return MockFunc()
    
    def get_func_name(self, ea):
        return f"sub_{ea:X}"
    
    def msg(self, text):
        print(f"[IDA] {text}")
    
    def register_action(self, desc):
        return True
    
    def create_menu(self, path, name, parent):
        return True
    
    def attach_action_to_menu(self, menu, action, flags):
        return True
    
    # 添加更多模拟方法
    PLUGIN_OK = 1
    PLUGIN_SKIP = 0
    PLUGIN_FIX = 1
    PLUGIN_HIDE = 2
    PLUGIN_MOD = 4
    AST_ENABLE_ALWAYS = 1
    SETMENU_APP = 0
    BWN_DISASM = 1
    MFF_WRITE = 1
    SN_FORCE = 1
    CIC_FUNC = 1
    FUNCATTR_END = 4


def setup_mock_environment():
    """设置模拟环境"""
    # 模拟IDA模块
    mock_modules = {
        'idaapi': MockIDAAPI(),
        'idc': MockIDAAPI(),
        'idautils': MockIDAAPI(),
        'ida_ida': MockIDAAPI(),
        'ida_kernwin': MockIDAAPI(),
    }
    
    for name, module in mock_modules.items():
        sys.modules[name] = module


def test_syntax_check(file_path):
    """测试语法检查"""
    print(f"\n=== 语法检查: {file_path} ===")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # 编译检查
        ast.parse(source_code)
        print("✅ 语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}, 位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_import_check(file_path):
    """测试导入检查"""
    print(f"\n=== 导入检查: {file_path} ===")
    try:
        # 设置模拟环境
        setup_mock_environment()
        
        # 动态导入模块
        spec = importlib.util.spec_from_file_location("test_module", file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print("✅ 导入检查通过")
        return True, module
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False, None
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False, None


def test_class_structure(module):
    """测试类结构"""
    print(f"\n=== 类结构检查 ===")
    try:
        # 检查主要类是否存在
        required_classes = [
            'FunctionRecognizer',
            'FingerManager', 
            'FingerUIManager',
            'FingerPlugin'
        ]
        
        for class_name in required_classes:
            if hasattr(module, class_name):
                print(f"✅ 类 {class_name} 存在")
            else:
                print(f"❌ 类 {class_name} 缺失")
                return False
        
        # 检查插件入口
        if hasattr(module, 'PLUGIN_ENTRY'):
            print("✅ 插件入口 PLUGIN_ENTRY 存在")
        else:
            print("❌ 插件入口 PLUGIN_ENTRY 缺失")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 类结构检查失败: {e}")
        return False


def test_config_validation(module):
    """测试配置验证"""
    print(f"\n=== 配置验证 ===")
    try:
        if hasattr(module, 'main_config'):
            config = module.main_config
            required_keys = ['url', 'headers', 'timeout', 'retry_count', 'num_threads', 'debug_mode']
            
            for key in required_keys:
                if key in config:
                    print(f"✅ 配置项 {key}: {config[key]}")
                else:
                    print(f"❌ 配置项 {key} 缺失")
                    return False
            
            # 验证配置值类型
            if isinstance(config['timeout'], int) and config['timeout'] > 0:
                print("✅ timeout 配置有效")
            else:
                print("❌ timeout 配置无效")
                return False
            
            if isinstance(config['retry_count'], int) and config['retry_count'] > 0:
                print("✅ retry_count 配置有效")
            else:
                print("❌ retry_count 配置无效")
                return False
            
            return True
        else:
            print("❌ main_config 配置缺失")
            return False
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False


def test_method_existence(module):
    """测试关键方法存在性"""
    print(f"\n=== 方法存在性检查 ===")
    try:
        # 测试FunctionRecognizer类的关键方法
        if hasattr(module, 'FunctionRecognizer'):
            fr = module.FunctionRecognizer()
            methods = ['get_func_feature', 'get_encode_Dectory', 'get', 'get2']
            
            for method in methods:
                if hasattr(fr, method):
                    print(f"✅ FunctionRecognizer.{method} 存在")
                else:
                    print(f"❌ FunctionRecognizer.{method} 缺失")
                    return False
        
        # 测试FingerManager类的关键方法
        if hasattr(module, 'FingerManager'):
            fm = module.FingerManager()
            methods = ['recognize_function_async', 'recognize_selected_function', 
                      'recognize_function_callback', 'recognize_functions_callback']
            
            for method in methods:
                if hasattr(fm, method):
                    print(f"✅ FingerManager.{method} 存在")
                else:
                    print(f"❌ FingerManager.{method} 缺失")
                    return False
        
        return True
    except Exception as e:
        print(f"❌ 方法存在性检查失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("IDA Pro 函数指纹识别插件测试")
    print("=" * 60)
    
    # 测试文件列表
    test_files = [
        'ida_function_fingerprint_plugin_final.py',
        '2.py',
        '1.py'
    ]
    
    results = {}
    
    for file_path in test_files:
        if not os.path.exists(file_path):
            print(f"\n⚠️  文件 {file_path} 不存在，跳过测试")
            continue
        
        print(f"\n{'='*20} 测试文件: {file_path} {'='*20}")
        
        # 语法检查
        syntax_ok = test_syntax_check(file_path)
        
        # 导入检查
        import_ok, module = test_import_check(file_path)
        
        tests_passed = 0
        total_tests = 2
        
        if syntax_ok:
            tests_passed += 1
        if import_ok:
            tests_passed += 1
        
        # 如果导入成功，进行更多测试
        if import_ok and module:
            # 类结构检查
            if test_class_structure(module):
                tests_passed += 1
            total_tests += 1
            
            # 配置验证
            if test_config_validation(module):
                tests_passed += 1
            total_tests += 1
            
            # 方法存在性检查
            if test_method_existence(module):
                tests_passed += 1
            total_tests += 1
        
        results[file_path] = {
            'passed': tests_passed,
            'total': total_tests,
            'success_rate': (tests_passed / total_tests) * 100
        }
    
    # 输出总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    
    for file_path, result in results.items():
        print(f"{file_path}:")
        print(f"  通过: {result['passed']}/{result['total']} ({result['success_rate']:.1f}%)")
        if result['success_rate'] >= 80:
            print(f"  状态: ✅ 良好")
        elif result['success_rate'] >= 60:
            print(f"  状态: ⚠️  需要改进")
        else:
            print(f"  状态: ❌ 需要修复")
        print()


if __name__ == "__main__":
    run_all_tests()
